"use client";

import { FC, useState } from 'react';
import { 
  MicIcon, 
  MicOffIcon, 
  VolumeXIcon, 
  Volume2Icon, 

  SettingsIcon,
  WifiIcon,
  WifiOffIcon,
  AlertTriangleIcon,
  ClockIcon,
  ActivityIcon
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { TooltipIconButton } from '@/components/assistant-ui/tooltip-icon-button';
import { cn } from '@/lib/utils';
import { ConnectionState } from '@/types/realtime';
import { SessionMetrics, VOICE_OPTIONS, VoiceOption } from '@/lib/session-config';

interface ControlPanelProps {
  // Connection state
  connectionState: ConnectionState;
  isConnected: boolean;
  
  // Audio states
  isRecording: boolean;
  isPlaying: boolean;
  isMuted: boolean;
  playbackRate: number;
  
  // Session info
  sessionMetrics: SessionMetrics;
  isSessionExpired: boolean;
  shouldShowWarning: boolean;
  
  // Controls
  onConnect: () => void;
  onDisconnect: () => void;
  onToggleRecording: () => void;
  onTriggerResponse: () => void;
  onToggleMute: () => void;
  onPlaybackRateChange: (rate: number) => void;
  onVoiceChange?: (voice: VoiceOption) => void;
  
  // Status
  error?: string | null;
}

export const ControlPanel: FC<ControlPanelProps> = ({
  connectionState,
  isConnected,
  isRecording,
  isPlaying,
  isMuted,
  playbackRate,
  sessionMetrics,
  isSessionExpired,
  shouldShowWarning,
  onConnect,
  onDisconnect,
  onToggleRecording,
  onTriggerResponse,
  onToggleMute,
  onPlaybackRateChange,
  onVoiceChange,
  error
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const [selectedVoice, setSelectedVoice] = useState<VoiceOption>('alloy');

  const playbackRates = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

  const formatDuration = (ms: number): string => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getConnectionStatusColor = () => {
    switch (connectionState) {
      case ConnectionState.CONNECTED:
        return 'text-green-600';
      case ConnectionState.CONNECTING:
        return 'text-yellow-600';
      case ConnectionState.ERROR:
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionState) {
      case ConnectionState.CONNECTED:
        return 'Connected';
      case ConnectionState.CONNECTING:
        return 'Connecting...';
      case ConnectionState.RECONNECTING:
        return 'Reconnecting...';
      case ConnectionState.ERROR:
        return 'Connection Error';
      default:
        return 'Disconnected';
    }
  };

  const handleVoiceChange = (voice: VoiceOption) => {
    setSelectedVoice(voice);
    onVoiceChange?.(voice);
  };

  return (
    <div className="bg-white/95 backdrop-blur-sm border-t border-slate-200/60 p-6 shadow-lg">
      {/* Status Bar */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-6">
          {/* Connection Status */}
          <div className="flex items-center gap-3 px-4 py-2 bg-slate-50 rounded-xl border border-slate-200/60">
            {isConnected ? (
              <WifiIcon className={cn("w-5 h-5", getConnectionStatusColor())} />
            ) : (
              <WifiOffIcon className={cn("w-5 h-5", getConnectionStatusColor())} />
            )}
            <div className="flex flex-col">
              <span className={cn("text-sm font-semibold", getConnectionStatusColor())}>
                {getConnectionStatusText()}
              </span>
              {isConnected && (
                <span className="text-xs text-slate-500">OpenAI Realtime API</span>
              )}
            </div>
          </div>

          {/* Session Timer */}
          {isConnected && (
            <div className="flex items-center gap-3 px-4 py-2 bg-slate-50 rounded-xl border border-slate-200/60">
              <ClockIcon className="w-5 h-5 text-slate-500" />
              <div className="flex flex-col">
                <span className={cn(
                  "text-sm font-semibold font-mono",
                  shouldShowWarning ? "text-orange-600" : "text-slate-700",
                  isSessionExpired ? "text-red-600" : ""
                )}>
                  {formatDuration(sessionMetrics.duration)}
                </span>
                <span className="text-xs text-slate-500">Session time</span>
              </div>
            </div>
          )}

          {/* Activity Indicator */}
          {(isRecording || isPlaying) && (
            <div className="flex items-center gap-3 px-4 py-2 bg-blue-50 rounded-xl border border-blue-200">
              <ActivityIcon className="w-5 h-5 text-blue-600 animate-pulse" />
              <div className="flex flex-col">
                <span className="text-sm font-semibold text-blue-700">
                  {isRecording && isPlaying ? 'Recording & Playing' :
                   isRecording ? 'Recording' : 'Playing'}
                </span>
                <span className="text-xs text-blue-500">Audio active</span>
              </div>
            </div>
          )}
        </div>

        {/* Warnings */}
        <div className="flex items-center gap-3">
          {shouldShowWarning && (
            <div className="flex items-center gap-2 px-4 py-2 bg-orange-50 text-orange-700 rounded-xl border border-orange-200 animate-pulse">
              <AlertTriangleIcon className="w-5 h-5" />
              <div className="flex flex-col">
                <span className="text-sm font-semibold">Session expires soon</span>
                <span className="text-xs">Consider reconnecting</span>
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-center gap-2 px-4 py-2 bg-red-50 text-red-700 rounded-xl border border-red-200 max-w-sm">
              <AlertTriangleIcon className="w-5 h-5 flex-shrink-0" />
              <div className="flex flex-col">
                <span className="text-sm font-semibold">Error</span>
                <span className="text-xs truncate">{error}</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* Connection Control */}
          {!isConnected ? (
            <Button
              onClick={onConnect}
              disabled={connectionState === ConnectionState.CONNECTING}
              className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
            >
              {connectionState === ConnectionState.CONNECTING ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Connecting...
                </div>
              ) : (
                'Connect to AI'
              )}
            </Button>
          ) : (
            <Button
              onClick={onDisconnect}
              variant="outline"
              className="border-2 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 font-semibold px-6 py-3 rounded-xl transition-all duration-200 transform hover:scale-105"
            >
              Disconnect
            </Button>
          )}

          <div className="w-px h-10 bg-slate-300" />

          {/* Recording Control */}
          <TooltipIconButton
            tooltip={isRecording ? "Stop Recording (R)" : "Start Recording (R)"}
            onClick={onToggleRecording}
            disabled={!isConnected}
            className={cn(
              "w-12 h-12 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-md",
              isRecording
                ? "bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 animate-pulse-recording"
                : "bg-gradient-to-r from-slate-100 to-slate-200 text-slate-600 hover:from-slate-200 hover:to-slate-300",
              !isConnected && "opacity-50 cursor-not-allowed transform-none"
            )}
          >
            {isRecording ? <MicOffIcon className="w-6 h-6" /> : <MicIcon className="w-6 h-6" />}
          </TooltipIconButton>

          {/* Trigger Response */}
          <Button
            onClick={onTriggerResponse}
            disabled={!isConnected}
            className={cn(
              "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105",
              !isConnected && "opacity-50 cursor-not-allowed transform-none"
            )}
          >
            <div className="flex items-center gap-2">
              <span>Trigger AI Response</span>
              <kbd className="bg-blue-800/30 px-2 py-1 rounded text-xs">Space</kbd>
            </div>
          </Button>

          <div className="w-px h-10 bg-slate-300" />

          {/* Audio Controls */}
          <TooltipIconButton
            tooltip={isMuted ? "Unmute (M)" : "Mute (M)"}
            onClick={onToggleMute}
            disabled={!isConnected}
            className={cn(
              "w-12 h-12 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-md",
              isMuted
                ? "bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700"
                : "bg-gradient-to-r from-slate-100 to-slate-200 text-slate-600 hover:from-slate-200 hover:to-slate-300",
              !isConnected && "opacity-50 cursor-not-allowed transform-none"
            )}
          >
            {isMuted ? <VolumeXIcon className="w-6 h-6" /> : <Volume2Icon className="w-6 h-6" />}
          </TooltipIconButton>

          {/* Playback Speed */}
          <div className="flex items-center gap-3 px-4 py-2 bg-slate-50 rounded-xl border border-slate-200">
            <span className="text-sm font-medium text-slate-700">Speed:</span>
            <select
              value={playbackRate}
              onChange={(e) => onPlaybackRateChange(parseFloat(e.target.value))}
              disabled={!isConnected}
              className={cn(
                "text-sm font-medium border-0 bg-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 rounded px-2 py-1 min-w-[70px]",
                !isConnected && "opacity-50 cursor-not-allowed"
              )}
            >
              {playbackRates.map(rate => (
                <option key={rate} value={rate}>
                  {rate}x
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Settings */}
        <div className="flex items-center gap-3">
          <TooltipIconButton
            tooltip="Settings & Statistics"
            onClick={() => setShowSettings(!showSettings)}
            className={cn(
              "w-12 h-12 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-md",
              showSettings
                ? "bg-gradient-to-r from-purple-500 to-purple-600 text-white"
                : "bg-gradient-to-r from-slate-100 to-slate-200 text-slate-600 hover:from-slate-200 hover:to-slate-300"
            )}
          >
            <SettingsIcon className="w-6 h-6" />
          </TooltipIconButton>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="mt-6 p-6 bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl border border-slate-200/60 shadow-lg animate-fade-in">
          <h3 className="text-lg font-bold text-slate-900 mb-6 flex items-center gap-2">
            <SettingsIcon className="w-5 h-5" />
            Settings & Statistics
          </h3>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Voice Selection */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-slate-700">
                AI Voice Selection
              </label>
              <select
                value={selectedVoice}
                onChange={(e) => handleVoiceChange(e.target.value as VoiceOption)}
                className="w-full text-sm border-2 border-slate-200 rounded-xl px-4 py-3 bg-white focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
              >
                {VOICE_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <p className="text-xs text-slate-500">Choose the AI voice that sounds best to you</p>
            </div>

            {/* Session Stats */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-slate-700">
                Session Statistics
              </label>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-white rounded-xl border border-slate-200">
                  <span className="text-sm text-slate-600">Messages</span>
                  <span className="text-sm font-bold text-slate-900">{sessionMetrics.messagesExchanged}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-white rounded-xl border border-slate-200">
                  <span className="text-sm text-slate-600">Audio chunks</span>
                  <span className="text-sm font-bold text-slate-900">{sessionMetrics.audioChunksProcessed}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-white rounded-xl border border-slate-200">
                  <span className="text-sm text-slate-600">Reconnections</span>
                  <span className="text-sm font-bold text-slate-900">{sessionMetrics.reconnectionCount}</span>
                </div>
              </div>
            </div>

            {/* Keyboard Shortcuts */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-slate-700">
                Keyboard Shortcuts
              </label>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-3 bg-white rounded-xl border border-slate-200">
                  <span className="text-sm text-slate-600">Trigger Response</span>
                  <kbd className="bg-slate-800 text-white px-3 py-1 rounded-lg text-xs font-mono">Space</kbd>
                </div>
                <div className="flex items-center justify-between p-3 bg-white rounded-xl border border-slate-200">
                  <span className="text-sm text-slate-600">Toggle Mute</span>
                  <kbd className="bg-slate-800 text-white px-3 py-1 rounded-lg text-xs font-mono">M</kbd>
                </div>
                <div className="flex items-center justify-between p-3 bg-white rounded-xl border border-slate-200">
                  <span className="text-sm text-slate-600">Toggle Recording</span>
                  <kbd className="bg-slate-800 text-white px-3 py-1 rounded-lg text-xs font-mono">R</kbd>
                </div>
                <div className="flex items-center justify-between p-3 bg-white rounded-xl border border-slate-200">
                  <span className="text-sm text-slate-600">Disconnect</span>
                  <kbd className="bg-slate-800 text-white px-3 py-1 rounded-lg text-xs font-mono">Esc</kbd>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
