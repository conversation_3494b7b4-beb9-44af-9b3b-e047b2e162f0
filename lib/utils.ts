import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Audio utility functions
export function base64ToArrayBuffer(base64: string): ArrayBuffer {
  try {
    if (!base64 || typeof base64 !== 'string') {
      throw new Error('Invalid base64 input');
    }

    // Clean the base64 string
    const cleanBase64 = base64.replace(/[^A-Za-z0-9+/]/g, '');

    const binaryString = atob(cleanBase64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
  } catch (error) {
    console.error('Error converting base64 to ArrayBuffer:', error);
    throw new Error(`Base64 conversion failed: ${error}`);
  }
}

export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  try {
    if (!buffer || buffer.byteLength === 0) {
      throw new Error('Invalid ArrayBuffer input');
    }

    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  } catch (error) {
    console.error('Error converting ArrayBuffer to base64:', error);
    throw new Error(`ArrayBuffer conversion failed: ${error}`);
  }
}

// PCM audio conversion utilities
export function convertFloat32ToPCM16(float32Array: Float32Array): ArrayBuffer {
  const pcm16 = new Int16Array(float32Array.length);
  for (let i = 0; i < float32Array.length; i++) {
    // Clamp to [-1, 1] and convert to 16-bit PCM
    const sample = Math.max(-1, Math.min(1, float32Array[i]));
    pcm16[i] = sample * 0x7FFF;
  }
  return pcm16.buffer;
}

export function convertPCM16ToFloat32(pcm16Buffer: ArrayBuffer): Float32Array {
  const pcm16 = new Int16Array(pcm16Buffer);
  const float32 = new Float32Array(pcm16.length);
  for (let i = 0; i < pcm16.length; i++) {
    float32[i] = pcm16[i] / 0x7FFF;
  }
  return float32;
}

// Session management utilities
export function formatSessionTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

export function isSessionNearTimeout(startTime: number, maxDuration: number = 30 * 60 * 1000): boolean {
  const elapsed = Date.now() - startTime;
  const remaining = maxDuration - elapsed;
  return remaining < 5 * 60 * 1000; // 5 minutes warning
}

// WebSocket connection utilities
export function createRealtimeWebSocket(ephemeralKey: string): WebSocket {
  const url = `wss://api.openai.com/v1/realtime?model=gpt-realtime-2025-08-28`;
  const ws = new WebSocket(url, [], {
    headers: {
      'Authorization': `Bearer ${ephemeralKey}`,
      'OpenAI-Beta': 'realtime=v1'
    }
  });
  return ws;
}

// Audio context utilities
export function createAudioContext(): AudioContext {
  const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
  return new AudioContextClass({
    sampleRate: 24000,
    latencyHint: 'interactive'
  });
}

// Keyboard event utilities
export function isSpacebarPressed(event: KeyboardEvent): boolean {
  return event.code === 'Space' && !event.repeat;
}

export function preventDefaultSpaceScroll(event: KeyboardEvent): void {
  if (event.code === 'Space' && event.target === document.body) {
    event.preventDefault();
  }
}
